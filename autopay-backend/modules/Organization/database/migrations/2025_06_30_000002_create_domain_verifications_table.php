<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('domain_verifications', function (Blueprint $table) {
            $table->ulid('id')->primary();
            $table->foreignUlid('domain_id')->constrained('domains')->cascadeOnDelete();
            
            // Verification details
            $table->string('verification_type'); // 'dns', 'file', 'meta'
            $table->string('verification_token');
            $table->string('verification_value')->nullable(); // Expected value for verification
            $table->text('verification_instructions')->nullable(); // Instructions for user
            
            // Status tracking
            $table->boolean('is_verified')->default(false);
            $table->timestamp('verified_at')->nullable();
            $table->timestamp('expires_at')->nullable(); // Token expiration
            $table->integer('verification_attempts')->default(0);
            $table->timestamp('last_check_at')->nullable();
            
            // Error tracking
            $table->text('last_error')->nullable();
            $table->json('verification_data')->nullable(); // Additional verification data
            
            $table->timestamps();
            
            // Indexes
            $table->index(['domain_id']);
            $table->index(['verification_type']);
            $table->index(['is_verified']);
            $table->index(['expires_at']);
            $table->index(['created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('domain_verifications');
    }
};
